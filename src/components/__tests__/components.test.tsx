import { render, screen } from '@testing-library/react';
import Header from '../Header';
import Hero from '../Hero';
import Search from '../Search';
import Footer from '../Footer';

// Mock tests to verify components render without errors
describe('Component Rendering Tests', () => {
  test('Header component renders', () => {
    render(<Header />);
    expect(screen.getByText('Trip.com')).toBeInTheDocument();
    expect(screen.getByText('Flights')).toBeInTheDocument();
    expect(screen.getByText('Hotels')).toBeInTheDocument();
  });

  test('Hero component renders', () => {
    render(<Hero />);
    expect(screen.getByText('Your Trip Starts Here')).toBeInTheDocument();
    expect(screen.getByText('Find the best deals on flights, hotels, and more')).toBeInTheDocument();
  });

  test('Search component renders', () => {
    render(<Search />);
    expect(screen.getByText('Flights')).toBeInTheDocument();
    expect(screen.getByText('Hotels')).toBeInTheDocument();
    expect(screen.getByText('Cars')).toBeInTheDocument();
    expect(screen.getByText('Packages')).toBeInTheDocument();
  });

  test('Footer component renders', () => {
    render(<Footer />);
    expect(screen.getByText('About Trip.com')).toBeInTheDocument();
    expect(screen.getByText('Support')).toBeInTheDocument();
    expect(screen.getByText('Follow Us')).toBeInTheDocument();
  });
});

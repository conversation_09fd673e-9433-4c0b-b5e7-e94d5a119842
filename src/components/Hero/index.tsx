'use client';

import React from 'react';

interface HeroProps {
  className?: string;
}

const Hero: React.FC<HeroProps> = ({ className = '' }) => {
  return (
    <section className={`bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20 ${className}`}>
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Your Trip Starts Here
        </h1>
        <p className="text-xl mb-8">
          Find the best deals on flights, hotels, and more
        </p>
      </div>
    </section>
  );
};

export default Hero;

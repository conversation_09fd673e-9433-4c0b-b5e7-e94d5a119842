'use client';

import React from 'react';

interface HeaderProps {
  className?: string;
}

const Header: React.FC<HeaderProps> = ({ className = '' }) => {
  const navigationItems = [
    'Flights',
    'Hotels', 
    'Cars',
    'Packages',
    'Cruises',
    'Things to do'
  ];

  return (
    <header className={`bg-white shadow-sm border-b border-gray-200 ${className}`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <div className="text-2xl font-bold text-blue-600">
              Trip.com
            </div>
            
            {/* Main Navigation */}
            <nav className="hidden md:flex space-x-6">
              {navigationItems.map((item) => (
                <a
                  key={item}
                  href="#"
                  className="text-gray-700 hover:text-blue-600 transition-colors"
                >
                  {item}
                </a>
              ))}
            </nav>
          </div>

          {/* Utility Navigation */}
          <div className="flex items-center space-x-4">
            {/* Currency Selector */}
            <select className="text-sm border border-gray-300 rounded px-2 py-1">
              <option>USD</option>
              <option>EUR</option>
              <option>GBP</option>
              <option>JPY</option>
            </select>
            
            {/* Sign In Button */}
            <button className="text-sm text-gray-700 hover:text-blue-600 transition-colors">
              Sign in
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;

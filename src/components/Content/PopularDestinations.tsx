'use client';

import React from 'react';

interface Destination {
  id: number;
  name: string;
  price: string;
  image?: string;
}

interface PopularDestinationsProps {
  className?: string;
}

const PopularDestinations: React.FC<PopularDestinationsProps> = ({ className = '' }) => {
  // Mock data for destinations
  const destinations: Destination[] = [
    { id: 1, name: 'Destination 1', price: 'From $299' },
    { id: 2, name: 'Destination 2', price: 'From $399' },
    { id: 3, name: 'Destination 3', price: 'From $499' },
    { id: 4, name: 'Destination 4', price: 'From $599' },
    { id: 5, name: 'Destination 5', price: 'From $699' },
    { id: 6, name: 'Destination 6', price: 'From $799' },
    { id: 7, name: 'Destination 7', price: 'From $899' },
    { id: 8, name: 'Destination 8', price: 'From $999' },
  ];

  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">
          Popular Destinations
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {destinations.map((destination) => (
            <div key={destination.id} className="trip-card p-4 cursor-pointer hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                <span className="text-gray-500">Image Placeholder</span>
              </div>
              <h3 className="font-semibold text-lg mb-2">
                {destination.name}
              </h3>
              <p className="text-gray-600 text-sm">{destination.price}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PopularDestinations;

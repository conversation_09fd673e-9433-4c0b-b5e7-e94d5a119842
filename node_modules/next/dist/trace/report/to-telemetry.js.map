{"version": 3, "sources": ["../../../src/trace/report/to-telemetry.ts"], "sourcesContent": ["import type { Telemetry } from '../../telemetry/storage'\nimport { traceGlobals } from '../shared'\nimport type { TraceEvent } from '../types'\n\nconst TRACE_EVENT_ACCESSLIST = new Map(\n  Object.entries({\n    'webpack-invalidated': 'WEBPACK_INVALIDATED',\n  })\n)\n\nconst reportToTelemetry = ({ name, duration }: TraceEvent) => {\n  const eventName = TRACE_EVENT_ACCESSLIST.get(name)\n  if (!eventName) {\n    return\n  }\n  const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n  if (!telemetry) {\n    return\n  }\n\n  telemetry.record({\n    eventName,\n    payload: {\n      durationInMicroseconds: duration,\n    },\n  })\n}\n\nexport default {\n  flushAll: () => {},\n  report: reportToTelemetry,\n}\n"], "names": ["TRACE_EVENT_ACCESSLIST", "Map", "Object", "entries", "reportToTelemetry", "name", "duration", "eventName", "get", "telemetry", "traceGlobals", "record", "payload", "durationInMicroseconds", "flushAll", "report"], "mappings": ";;;;+BA4BA;;;eAAA;;;wBA3B6B;AAG7B,MAAMA,yBAAyB,IAAIC,IACjCC,OAAOC,OAAO,CAAC;IACb,uBAAuB;AACzB;AAGF,MAAMC,oBAAoB,CAAC,EAAEC,IAAI,EAAEC,QAAQ,EAAc;IACvD,MAAMC,YAAYP,uBAAuBQ,GAAG,CAACH;IAC7C,IAAI,CAACE,WAAW;QACd;IACF;IACA,MAAME,YAAmCC,oBAAY,CAACF,GAAG,CAAC;IAC1D,IAAI,CAACC,WAAW;QACd;IACF;IAEAA,UAAUE,MAAM,CAAC;QACfJ;QACAK,SAAS;YACPC,wBAAwBP;QAC1B;IACF;AACF;MAEA,WAAe;IACbQ,UAAU,KAAO;IACjBC,QAAQX;AACV"}
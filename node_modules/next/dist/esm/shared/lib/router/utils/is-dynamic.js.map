{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/is-dynamic.ts"], "sourcesContent": ["import {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from './interception-routes'\n\n// Identify /.*[param].*/ in route string\nconst TEST_ROUTE = /\\/[^/]*\\[[^/]+\\][^/]*(?=\\/|$)/\n\n// Identify /[param]/ in route string\nconst TEST_STRICT_ROUTE = /\\/\\[[^/]+\\](?=\\/|$)/\n\n/**\n * Check if a route is dynamic.\n *\n * @param route - The route to check.\n * @param strict - Whether to use strict mode which prohibits segments with prefixes/suffixes (default: true).\n * @returns Whether the route is dynamic.\n */\nexport function isDynamicRoute(route: string, strict: boolean = true): boolean {\n  if (isInterceptionRouteAppPath(route)) {\n    route = extractInterceptionRouteInformation(route).interceptedRoute\n  }\n\n  if (strict) {\n    return TEST_STRICT_ROUTE.test(route)\n  }\n\n  return TEST_ROUTE.test(route)\n}\n"], "names": ["extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "TEST_ROUTE", "TEST_STRICT_ROUTE", "isDynamicRoute", "route", "strict", "interceptedRoute", "test"], "mappings": "AAAA,SACEA,mCAAmC,EACnCC,0BAA0B,QACrB,wBAAuB;AAE9B,yCAAyC;AACzC,MAAMC,aAAa;AAEnB,qCAAqC;AACrC,MAAMC,oBAAoB;AAE1B;;;;;;CAMC,GACD,OAAO,SAASC,eAAeC,KAAa,EAAEC,MAAsB;IAAtBA,IAAAA,mBAAAA,SAAkB;IAC9D,IAAIL,2BAA2BI,QAAQ;QACrCA,QAAQL,oCAAoCK,OAAOE,gBAAgB;IACrE;IAEA,IAAID,QAAQ;QACV,OAAOH,kBAAkBK,IAAI,CAACH;IAChC;IAEA,OAAOH,WAAWM,IAAI,CAACH;AACzB"}
{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\n\nexport interface ParsedUrl {\n  hash: string\n  hostname?: string | null\n  href: string\n  pathname: string\n  port?: string | null\n  protocol?: string | null\n  query: ParsedUrlQuery\n  search: string\n}\n\nexport function parseUrl(url: string): ParsedUrl {\n  if (url.startsWith('/')) {\n    return parseRelativeUrl(url)\n  }\n\n  const parsedURL = new URL(url)\n  return {\n    hash: parsedURL.hash,\n    hostname: parsedURL.hostname,\n    href: parsedURL.href,\n    pathname: parsedURL.pathname,\n    port: parsedURL.port,\n    protocol: parsedURL.protocol,\n    query: searchParamsToUrlQuery(parsedURL.searchParams),\n    search: parsedURL.search,\n  }\n}\n"], "names": ["searchParamsToUrlQuery", "parseRelativeUrl", "parseUrl", "url", "startsWith", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParams", "search"], "mappings": "AAEA,SAASA,sBAAsB,QAAQ,gBAAe;AACtD,SAASC,gBAAgB,QAAQ,uBAAsB;AAavD,OAAO,SAASC,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOH,iBAAiBE;IAC1B;IAEA,MAAME,YAAY,IAAIC,IAAIH;IAC1B,OAAO;QACLI,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOb,uBAAuBK,UAAUS,YAAY;QACpDC,QAAQV,UAAUU,MAAM;IAC1B;AACF"}
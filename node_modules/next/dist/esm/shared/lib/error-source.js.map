{"version": 3, "sources": ["../../../src/shared/lib/error-source.ts"], "sourcesContent": ["const symbolError = Symbol.for('NextjsError')\n\nexport function getErrorSource(error: Error): 'server' | 'edge-server' | null {\n  return (error as any)[symbolError] || null\n}\n\nexport type ErrorSourceType = 'edge-server' | 'server'\n\nexport function decorateServerError(error: Error, type: ErrorSourceType) {\n  Object.defineProperty(error, symbolError, {\n    writable: false,\n    enumerable: false,\n    configurable: false,\n    value: type,\n  })\n}\n"], "names": ["symbolError", "Symbol", "for", "getErrorSource", "error", "decorateServerError", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value"], "mappings": "AAAA,MAAMA,cAAcC,OAAOC,GAAG,CAAC;AAE/B,OAAO,SAASC,eAAeC,KAAY;IACzC,OAAO,AAACA,KAAa,CAACJ,YAAY,IAAI;AACxC;AAIA,OAAO,SAASK,oBAAoBD,KAAY,EAAEE,IAAqB;IACrEC,OAAOC,cAAc,CAACJ,OAAOJ,aAAa;QACxCS,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF"}
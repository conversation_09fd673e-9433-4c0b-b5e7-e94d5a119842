{"version": 3, "sources": ["../../../src/shared/lib/html-context.shared-runtime.ts"], "sourcesContent": ["import type { BuildManifest } from '../../server/get-page-files'\nimport type { ServerRuntime } from '../../types'\nimport type { NEXT_DATA } from './utils'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { DeepReadonly } from './deep-readonly'\n\nimport { createContext, useContext, type JSX } from 'react'\n\nexport type HtmlProps = {\n  __NEXT_DATA__: NEXT_DATA\n  strictNextHead: boolean\n  dangerousAsPath: string\n  docComponentsRendered: {\n    Html?: boolean\n    Main?: boolean\n    Head?: boolean\n    NextScript?: boolean\n  }\n  buildManifest: BuildManifest\n  ampPath: string\n  inAmpMode: boolean\n  hybridAmp: boolean\n  isDevelopment: boolean\n  dynamicImports: string[]\n  /**\n   * This manifest is only needed for Pages dir, Production, Webpack\n   * @see https://github.com/vercel/next.js/pull/72959\n   */\n  dynamicCssManifest: Set<string>\n  assetPrefix?: string\n  canonicalBase: string\n  headTags: any[]\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  assetQueryString: string\n  scriptLoader: {\n    afterInteractive?: string[]\n    beforeInteractive?: any[]\n    worker?: any[]\n  }\n  locale?: string\n  disableOptimizedLoading?: boolean\n  styles?: React.ReactElement[] | Iterable<React.ReactNode>\n  head?: Array<JSX.Element | null>\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  optimizeCss?: any\n  nextConfigOutput?: 'standalone' | 'export'\n  nextScriptWorkers?: boolean\n  runtime?: ServerRuntime\n  hasConcurrentFeatures?: boolean\n  largePageDataBytes?: number\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  experimentalClientTraceMetadata?: string[]\n}\n\nexport const HtmlContext = createContext<HtmlProps | undefined>(undefined)\nif (process.env.NODE_ENV !== 'production') {\n  HtmlContext.displayName = 'HtmlContext'\n}\n\nexport function useHtmlContext() {\n  const context = useContext(HtmlContext)\n\n  if (!context) {\n    throw new Error(\n      `<Html> should not be imported outside of pages/_document.\\n` +\n        'Read more: https://nextjs.org/docs/messages/no-document-import-in-page'\n    )\n  }\n\n  return context\n}\n"], "names": ["createContext", "useContext", "HtmlContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useHtmlContext", "context", "Error"], "mappings": "AAMA,SAASA,aAAa,EAAEC,UAAU,QAAkB,QAAO;AAiD3D,OAAO,MAAMC,cAAcF,cAAqCG,WAAU;AAC1E,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,YAAYK,WAAW,GAAG;AAC5B;AAEA,OAAO,SAASC;IACd,MAAMC,UAAUR,WAAWC;IAE3B,IAAI,CAACO,SAAS;QACZ,MAAM,qBAGL,CAHK,IAAIC,MACR,AAAC,gEACC,2EAFE,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,OAAOD;AACT"}
{"version": 3, "sources": ["../../../src/shared/lib/match-local-pattern.ts"], "sourcesContent": ["import type { LocalPattern } from './image-config'\nimport { makeRe } from 'next/dist/compiled/picomatch'\n\n// Modifying this function should also modify writeImagesManifest()\nexport function matchLocalPattern(pattern: LocalPattern, url: URL): boolean {\n  if (pattern.search !== undefined) {\n    if (pattern.search !== url.search) {\n      return false\n    }\n  }\n\n  if (!makeRe(pattern.pathname ?? '**', { dot: true }).test(url.pathname)) {\n    return false\n  }\n\n  return true\n}\n\nexport function hasLocalMatch(\n  localPatterns: LocalPattern[] | undefined,\n  urlPathAndQuery: string\n): boolean {\n  if (!localPatterns) {\n    // if the user didn't define \"localPatterns\", we allow all local images\n    return true\n  }\n  const url = new URL(urlPathAndQuery, 'http://n')\n  return localPatterns.some((p) => matchLocalPattern(p, url))\n}\n"], "names": ["makeRe", "matchLocalPattern", "pattern", "url", "search", "undefined", "pathname", "dot", "test", "hasLocalMatch", "localPatterns", "urlPathAndQuery", "URL", "some", "p"], "mappings": "AACA,SAASA,MAAM,QAAQ,+BAA8B;AAErD,mEAAmE;AACnE,OAAO,SAASC,kBAAkBC,OAAqB,EAAEC,GAAQ;IAC/D,IAAID,QAAQE,MAAM,KAAKC,WAAW;QAChC,IAAIH,QAAQE,MAAM,KAAKD,IAAIC,MAAM,EAAE;YACjC,OAAO;QACT;IACF;QAEYF;IAAZ,IAAI,CAACF,OAAOE,CAAAA,oBAAAA,QAAQI,QAAQ,YAAhBJ,oBAAoB,MAAM;QAAEK,KAAK;IAAK,GAAGC,IAAI,CAACL,IAAIG,QAAQ,GAAG;QACvE,OAAO;IACT;IAEA,OAAO;AACT;AAEA,OAAO,SAASG,cACdC,aAAyC,EACzCC,eAAuB;IAEvB,IAAI,CAACD,eAAe;QAClB,uEAAuE;QACvE,OAAO;IACT;IACA,MAAMP,MAAM,IAAIS,IAAID,iBAAiB;IACrC,OAAOD,cAAcG,IAAI,CAAC,CAACC,IAAMb,kBAAkBa,GAAGX;AACxD"}
{"version": 3, "sources": ["../../../src/shared/lib/get-hostname.ts"], "sourcesContent": ["import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n"], "names": ["getHostname", "parsed", "headers", "hostname", "host", "Array", "isArray", "toString", "split", "toLowerCase"], "mappings": "AAEA;;;;;CAKC,GACD,OAAO,SAASA,YACdC,MAAoC,EACpCC,OAA6B;IAE7B,2EAA2E;IAC3E,YAAY;IACZ,IAAIC;IACJ,IAAID,CAAAA,2BAAAA,QAASE,IAAI,KAAI,CAACC,MAAMC,OAAO,CAACJ,QAAQE,IAAI,GAAG;QACjDD,WAAWD,QAAQE,IAAI,CAACG,QAAQ,GAAGC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;IACrD,OAAO,IAAIP,OAAOE,QAAQ,EAAE;QAC1BA,WAAWF,OAAOE,QAAQ;IAC5B,OAAO;IAEP,OAAOA,SAASM,WAAW;AAC7B"}
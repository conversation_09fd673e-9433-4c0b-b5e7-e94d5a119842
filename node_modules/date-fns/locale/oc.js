import { formatDistance } from "./oc/_lib/formatDistance.js";
import { formatLong } from "./oc/_lib/formatLong.js";
import { formatRelative } from "./oc/_lib/formatRelative.js";
import { localize } from "./oc/_lib/localize.js";
import { match } from "./oc/_lib/match.js";

/**
 * @category Locales
 * @summary Occitan locale.
 * @language Occitan
 * @iso-639-2 oci
 * <AUTHOR> PAGÈS
 */
export const oc = {
  code: "oc",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default oc;
